<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智能查询助手</title>
  <script src="https://cdn.jsdelivr.net/npm/marked@4.0.18/marked.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <link rel="icon" type="image/png" href="https://peiwan.bs2dl.yy.com/c0df986ba919482cb41e59352bd8f383.png">
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --success: #10b981;
      --warning: #f59e0b;
      --error: #ef4444;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      color: var(--text-primary);
      margin: 0;
      padding: 0;
      overflow-x: hidden;
    }

    /* 滚动条样式 */
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-track {
      background: var(--bg-secondary);
    }

    ::-webkit-scrollbar-thumb {
      background: var(--border-primary);
      border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: var(--border-secondary);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .sidebar {
        position: fixed;
        left: -100%;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
        width: 280px !important;
      }

      .sidebar.open {
        left: 0;
      }

      .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .mobile-header {
        display: flex !important;
      }

      .desktop-header {
        display: none !important;
      }
    }

    @media (min-width: 769px) {
      .mobile-header {
        display: none !important;
      }

      .desktop-header {
        display: flex !important;
      }
    }

    /* Grok风格的消息气泡样式 */
    .message-bubble {
      border-radius: var(--radius-xl);
      padding: 1.25rem 1.5rem;
      margin-bottom: 1.5rem;
      line-height: 1.7;
      backdrop-filter: blur(10px);
      word-break: break-word;
      overflow-wrap: break-word;
      position: relative;
      transition: all 0.2s ease;
    }

    .user-message {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      align-self: flex-end;
      border-bottom-right-radius: var(--radius-sm);
      box-shadow: var(--shadow-md);
      border: 1px solid var(--border-primary); /* 用户消息保留边框 */
      max-width: 75%; /* 用户消息保持较小宽度 */
    }

    .assistant-message {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      align-self: flex-start;
      border-bottom-left-radius: var(--radius-sm);
      box-shadow: var(--shadow-sm);
      border: none; /* 完全取消AI消息的边框 */
      width: 100%; /* AI消息使用全宽 */
      max-width: none; /* 移除最大宽度限制 */
    }

    /* 头像样式优化 */
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
      box-shadow: var(--shadow-sm);
      border: 2px solid var(--border-primary);
    }

    .user-avatar {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
    }

    .assistant-avatar {
      background: linear-gradient(135deg, var(--success), #059669);
      color: white;
    }

    /* Markdown 内容样式 */
    .markdown-content {
      line-height: 1.7;
    }

    .markdown-content p {
      margin-bottom: 1rem;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .markdown-content ul, .markdown-content ol {
      margin: 1rem 0;
      padding-left: 1.5rem;
    }

    .markdown-content li {
      margin-bottom: 0.5rem;
    }

    .markdown-content strong {
      font-weight: 600;
      color: var(--text-primary);
    }

    .markdown-content code {
      background: var(--bg-secondary);
      color: var(--accent-primary);
      padding: 0.125rem 0.375rem;
      border-radius: var(--radius-sm);
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      font-size: 0.875em;
      border: 1px solid var(--border-primary);
    }

    .markdown-content pre {
      background: var(--bg-secondary);
      padding: 1rem;
      border-radius: var(--radius-md);
      overflow-x: auto;
      margin: 1rem 0;
      border: 1px solid var(--border-primary);
      box-shadow: var(--shadow-sm);
    }

    .markdown-content pre code {
      background: transparent;
      padding: 0;
      border: none;
      color: var(--text-primary);
    }

    .markdown-content table {
      border-collapse: collapse;
      margin: 1rem 0;
      width: 100%;
      border-radius: var(--radius-md);
      overflow: hidden;
      border: 1px solid var(--border-primary);
      font-size: 0.9rem; /* 稍微减小字体以适应更多内容 */
    }

    .markdown-content th,
    .markdown-content td {
      border: 1px solid var(--border-primary);
      padding: 0.75rem 1rem; /* 增加水平内边距 */
      text-align: left;
      vertical-align: top; /* 顶部对齐，适合多行内容 */
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .markdown-content th {
      background: var(--bg-secondary);
      font-weight: 600;
      color: var(--text-primary);
      white-space: nowrap; /* 表头不换行 */
    }

    /* 表格响应式处理 */
    .markdown-content .table-container {
      overflow-x: auto;
      margin: 1rem 0;
      border-radius: var(--radius-md);
      border: 1px solid var(--border-primary);
    }

    .markdown-content .table-container table {
      margin: 0;
      border: none;
      border-radius: 0;
    }

    .markdown-content img {
      max-width: 100%;
      height: auto;
      border-radius: var(--radius-md);
      margin: 1rem 0;
      box-shadow: var(--shadow-sm);
    }

    .markdown-content blockquote {
      border-left: 4px solid var(--accent-primary);
      padding-left: 1rem;
      margin: 1rem 0;
      color: var(--text-secondary);
      background: var(--accent-light);
      padding: 1rem;
      border-radius: var(--radius-md);
    }
    /* 侧边栏样式 */
    .sidebar {
      background: var(--bg-secondary);
      border-right: 1px solid var(--border-primary);
      backdrop-filter: blur(20px);
    }

    .chat-item {
      border-radius: var(--radius-md);
      transition: all 0.2s ease;
      border: 1px solid transparent;
    }

    .chat-item:hover {
      background: var(--bg-hover);
      border-color: var(--border-secondary);
      transform: translateX(2px);
    }

    .chat-item.active {
      background: var(--accent-light);
      border-color: var(--accent-primary);
      color: var(--accent-primary);
    }

    /* Cherry Studio 风格的工具选择器 */
    .tool-selector {
      position: relative;
      margin-bottom: 0.75rem;
    }

    .tool-dropdown {
      position: absolute;
      bottom: 100%;
      left: 0;
      right: 0;
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      backdrop-filter: blur(20px);
      z-index: 100;
      max-height: 200px;
      overflow-y: auto;
      opacity: 0;
      visibility: hidden;
      transform: translateY(10px);
      transition: all 0.2s ease;
    }

    .tool-dropdown.show {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .tool-option {
      padding: 0.75rem 1rem;
      cursor: pointer;
      border-bottom: 1px solid var(--border-primary);
      transition: all 0.15s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .tool-option:last-child {
      border-bottom: none;
    }

    .tool-option:hover {
      background: var(--bg-hover);
    }

    .tool-option.selected {
      background: var(--accent-light);
      color: var(--accent-primary);
    }

    .tool-icon {
      width: 16px;
      height: 16px;
      opacity: 0.7;
    }

    .tool-name {
      font-weight: 500;
      font-size: 0.875rem;
    }

    .tool-description {
      font-size: 0.75rem;
      color: var(--text-tertiary);
      margin-top: 0.125rem;
    }

    .tool-trigger {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 0.75rem;
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 0.875rem;
      color: var(--text-secondary);
    }

    .tool-trigger:hover {
      border-color: var(--accent-primary);
      background: var(--accent-light);
    }

    .tool-trigger.active {
      border-color: var(--accent-primary);
      color: var(--accent-primary);
    }

    /* 输入框操作按钮区域 */
    .input-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    /* 大模型提供商选择按钮 - Grok风格 */
    .model-provider-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.5rem 0.75rem;
      min-width: 120px;
      height: 40px;
      border-radius: var(--radius-lg);
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      cursor: pointer;
      transition: all 0.2s ease;
      color: var(--text-primary);
      position: relative;
      flex-shrink: 0;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .model-provider-btn.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: var(--bg-secondary);
    }

    /* 模型提供商按钮容器 */
    .model-provider-container {
      position: relative;
      display: inline-block;
    }

    .model-provider-btn:hover:not(.disabled) {
      background: var(--bg-hover);
      border-color: var(--border-primary);
      color: var(--text-primary);
    }

    .model-provider-btn.active {
      background: var(--accent-light);
      border-color: var(--accent-primary);
      color: var(--accent-primary);
    }

    .model-provider-text {
      flex: 1;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .model-provider-icon {
      width: 16px;
      height: 16px;
      flex-shrink: 0;
    }

    /* Thinking按钮样式 - Grok风格 */
    .thinking-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.375rem;
      padding: 0.5rem 0.75rem;
      height: 40px;
      border-radius: var(--radius-lg);
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      cursor: pointer;
      transition: all 0.2s ease;
      color: var(--text-secondary);
      position: relative;
      flex-shrink: 0;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .thinking-btn:hover {
      background: var(--bg-hover);
      border-color: var(--border-primary);
      color: var(--text-primary);
    }

    .thinking-btn:hover .thinking-icon {
      filter: brightness(0.8); /* hover时稍微亮一些 */
      color: #9ca3af; /* 稍微亮一些的灰色 */
    }

    .thinking-btn.active {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      border-color: var(--accent-primary);
      color: white;
      box-shadow: 0 0 0 2px var(--accent-light);
    }

    .thinking-btn.active .thinking-icon {
      filter: brightness(1.5) drop-shadow(0 0 4px currentColor);
      color: #fbbf24; /* 温暖的黄色，模拟灯泡点亮 */
    }

    .thinking-icon {
      width: 16px;
      height: 16px;
      transition: all 0.3s ease;
      filter: brightness(0.6); /* 默认状态较暗，模拟灯泡熄灭 */
      color: #6b7280; /* 灰色，模拟熄灭状态 */
    }

    /* 模型提供商下拉框 */
    .model-provider-dropdown {
      position: absolute;
      bottom: 100%;
      left: 0;
      min-width: 320px;
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      max-height: 300px;
      overflow-y: auto;
      z-index: 1000;
      margin-bottom: 0.5rem;
      opacity: 0;
      visibility: hidden;
      transform: translateY(10px);
      transition: all 0.2s ease;
    }

    .model-provider-dropdown.show {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .provider-group {
      border-bottom: 1px solid var(--border-secondary);
    }

    .provider-group:last-child {
      border-bottom: none;
    }

    .provider-header {
      padding: 0.75rem 1rem;
      background: var(--bg-tertiary);
      border-bottom: 1px solid var(--border-secondary);
      font-weight: 600;
      font-size: 0.875rem;
      color: var(--text-primary);
    }

    .model-option {
      padding: 0.75rem 1rem;
      cursor: pointer;
      transition: all 0.2s ease;
      border-bottom: 1px solid var(--border-secondary);
    }

    .model-option:last-child {
      border-bottom: none;
    }

    .model-option:hover {
      background: var(--bg-hover);
    }

    .model-option.selected {
      background: var(--accent-light);
      color: var(--accent-primary);
    }

    .model-name {
      font-weight: 500;
      font-size: 0.875rem;
      color: var(--text-primary);
      margin-bottom: 0.25rem;
    }

    .model-description {
      font-size: 0.75rem;
      color: var(--text-tertiary);
      line-height: 1.4;
    }


    /* 现代化输入框样式 */
    .input-container {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-xl);
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);
      box-shadow: var(--shadow-sm);
    }

    .input-container:focus-within {
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 3px var(--accent-light);
    }

    .message-input {
      background: transparent;
      border: none;
      outline: none;
      color: var(--text-primary);
      font-size: 0.95rem;
      line-height: 1.5;
      resize: none;
    }

    .message-input::placeholder {
      color: var(--text-tertiary);
    }

    /* 按钮样式优化 */
    .btn-primary {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: var(--shadow-md);
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }

    .btn-primary:active {
      transform: translateY(0);
    }

    .btn-primary:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .btn-secondary {
      background: var(--bg-tertiary);
      color: var(--text-secondary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-lg);
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .btn-secondary:hover {
      background: var(--bg-hover);
      border-color: var(--border-primary);
      color: var(--text-primary);
    }

    .send-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      margin-left: 0.5rem;
    }

    .stop-btn {
      background: linear-gradient(135deg, var(--error), #dc2626);
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      margin-left: 0.5rem;
    }

    /* 优雅的打字指示器 */
    .typing-indicator {
      display: inline-flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.5rem 0;
    }

    .typing-indicator span {
      height: 6px;
      width: 6px;
      background: var(--accent-primary);
      border-radius: 50%;
      display: inline-block;
      animation: typing-bounce 1.4s infinite ease-in-out both;
    }

    .typing-indicator span:nth-child(1) {
      animation-delay: -0.32s;
    }

    .typing-indicator span:nth-child(2) {
      animation-delay: -0.16s;
    }

    .typing-indicator span:nth-child(3) {
      animation-delay: 0s;
    }

    @keyframes typing-bounce {
      0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
      }
      40% {
        transform: scale(1.2);
        opacity: 1;
      }
    }

    /* 空状态样式 */
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      text-align: center;
      padding: 2rem;
    }

    .empty-state-icon {
      width: 64px;
      height: 64px;
      margin-bottom: 1.5rem;
      opacity: 0.3;
    }

    .empty-state-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--text-primary);
    }

    .empty-state-description {
      color: var(--text-tertiary);
      max-width: 400px;
      line-height: 1.6;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
      .user-message {
        max-width: 90%; /* 移动端用户消息稍微增大 */
      }

      .assistant-message {
        width: 100%; /* 移动端AI消息仍然全宽 */
        padding: 1rem 1.25rem; /* 移动端稍微减少内边距 */
      }

      .message-bubble {
        padding: 0.875rem 1rem;
      }

      .avatar {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }

      .tool-dropdown {
        max-height: 150px;
      }

      .empty-state-title {
        font-size: 1.25rem;
      }

      .empty-state-description {
        font-size: 0.875rem;
      }

      /* 移动端思维链优化 */
      .thinking-chain-header {
        padding: 0.75rem 1rem;
      }

      .thinking-chain-inner {
        padding: 1rem;
        font-size: 0.8rem;
      }

      .thinking-chain-title {
        font-size: 0.8rem;
      }

      .thinking-chain-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
      }
    }

    /* 思维链样式 - 参考grok.com风格 */
    .thinking-chain {
      background: var(--bg-secondary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-lg);
      margin: 1rem 0;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .thinking-chain-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.875rem 1.25rem;
      cursor: pointer;
      background: var(--bg-tertiary);
      border-bottom: 1px solid var(--border-secondary);
      transition: all 0.2s ease;
      user-select: none;
    }

    .thinking-chain-header:hover {
      background: var(--bg-hover);
    }

    .thinking-chain-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--text-secondary);
    }

    .thinking-chain-icon {
      width: 16px;
      height: 16px;
      transition: transform 0.2s ease;
      color: var(--accent-primary);
    }

    .thinking-chain-header.expanded .thinking-chain-icon {
      transform: rotate(90deg);
    }

    .thinking-chain-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }

    .thinking-chain-content.expanded {
      max-height: 1000px; /* 足够大的值以容纳内容 */
    }

    .thinking-chain-inner {
      padding: 1.25rem;
      color: var(--text-tertiary);
      font-size: 0.875rem;
      line-height: 1.6;
      white-space: pre-wrap;
      word-wrap: break-word;
      overflow-wrap: break-word;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    }

    .thinking-chain-badge {
      background: var(--accent-light);
      color: var(--accent-primary);
      padding: 0.25rem 0.5rem;
      border-radius: var(--radius-sm);
      font-size: 0.75rem;
      font-weight: 500;
    }

    /* 思维链动画效果 */
    .thinking-chain.thinking {
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 1px var(--accent-light);
      animation: thinking-pulse 2s ease-in-out infinite;
    }

    .thinking-chain.thinking .thinking-chain-header {
      background: var(--accent-light);
    }

    .thinking-chain.thinking .thinking-chain-title {
      color: var(--accent-primary);
    }

    .thinking-chain.thinking .thinking-chain-badge {
      animation: thinking-badge-pulse 1.5s ease-in-out infinite;
    }

    /* 思考中的脉动动画 */
    @keyframes thinking-pulse {
      0%, 100% {
        box-shadow: 0 0 0 1px var(--accent-light);
      }
      50% {
        box-shadow: 0 0 0 3px var(--accent-light);
      }
    }

    @keyframes thinking-badge-pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: 0.6;
      }
    }

    /* 思维链内容的打字效果 */
    .thinking-chain-inner.typing::after {
      content: '▋';
      animation: typing-cursor 1s infinite;
      color: var(--accent-primary);
    }

    @keyframes typing-cursor {
      0%, 50% {
        opacity: 1;
      }
      51%, 100% {
        opacity: 0;
      }
    }

    /* 大屏幕优化 - 为AI消息提供更好的阅读体验 */
    @media (min-width: 1200px) {
      .assistant-message {
        padding: 1.5rem 2rem; /* 大屏幕增加内边距 */
      }

      .markdown-content {
        font-size: 1rem; /* 稍微增大字体 */
        line-height: 1.8; /* 增加行高 */
      }
    }
  </style>
</head>
<body>
  <!-- Thymeleaf变量显示 -->
  <div th:if="${message}" class="hidden" id="serverMessage" th:text="${message}"></div>

  <!-- 移动端遮罩层 -->
  <div class="sidebar-overlay" id="sidebarOverlay"></div>

  <div class="flex h-screen">
    <!-- 侧边栏 -->
    <div class="w-72 sidebar p-4 flex flex-col" id="sidebar">
      <button
        id="newChatBtn"
        class="mb-6 btn-primary"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        新建对话
      </button>
      <div id="chatList" class="flex-1 overflow-y-auto space-y-2">
        <!-- 聊天列表将通过JavaScript动态生成 -->
        <div class="text-center mt-8" id="noChatMessage" style="color: var(--text-tertiary); font-size: 0.875rem;">
          没有对话记录
        </div>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 头部区域 -->
      <div class="flex justify-between items-center p-4 border-b border-opacity-10" style="border-color: var(--border-primary);">
        <!-- 移动端菜单按钮 -->
        <button
          id="mobileMenuBtn"
          class="mobile-header btn-secondary p-2"
          style="display: none;"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        <!-- 桌面端标题 -->
        <div class="desktop-header flex items-center">
          <h1 class="text-lg font-semibold" style="color: var(--text-primary);">Yo</h1>
        </div>

        <!-- 退出登录按钮 -->
        <button
          id="logoutBtn"
          class="btn-secondary"
          title="退出登录"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
          <span class="hidden sm:inline">退出登录</span>
        </button>
      </div>

      <!-- 消息区域 -->
      <div class="flex-1 overflow-y-auto p-4 md:p-6" id="messageContainer">
        <div id="emptyState" class="empty-state">
          <svg xmlns="http://www.w3.org/2000/svg" class="empty-state-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          <h2 class="empty-state-title">开始新的对话</h2>
          <p class="empty-state-description">
            输入您的问题，开始与智能助手对话。
          </p>
        </div>
        <div id="messagesWrapper" class="max-w-6xl mx-auto hidden">
          <!-- 消息将通过JavaScript动态生成 -->
        </div>
      </div>
      <!-- 输入区域 -->
      <div class="p-4" style="background: var(--bg-secondary); border-top: 1px solid var(--border-primary);">
        <div class="max-w-6xl mx-auto">


          <!-- Cherry Studio 风格的工具选择器 -->
          <div class="tool-selector" id="toolSelector">
            <div class="tool-dropdown" id="toolDropdown">
              <!-- 工具选项将通过JavaScript动态生成 -->
            </div>
            <div class="tool-trigger" id="toolTrigger">
              <svg xmlns="http://www.w3.org/2000/svg" class="tool-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 9.172V5L8 4z" />
              </svg>
              <span id="toolTriggerText">选择工具</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="tool-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>

          <!-- 输入框 -->
          <form id="messageForm" class="input-container p-3 flex items-end gap-3">
            <textarea
              id="messageInput"
              class="flex-1 message-input p-2 text-base resize-none min-h-[48px] max-h-[200px]"
              placeholder="输入您的问题..."
              rows="1"
            ></textarea>
            <div class="input-actions">
              <!-- Thinking按钮 -->
              <button
                type="button"
                id="thinkingBtn"
                class="thinking-btn"
                title="深度思考模式"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="thinking-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <span>Think</span>
              </button>

              <!-- 大模型提供商选择按钮容器 -->
              <div class="model-provider-container">
                <button
                  type="button"
                  id="modelProviderBtn"
                  class="model-provider-btn"
                  title="选择大模型"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="model-provider-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span class="model-provider-text" id="modelProviderText">选择模型</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="model-provider-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                <!-- 模型选择下拉框 -->
                <div class="model-provider-dropdown" id="modelProviderDropdown">
                  <!-- 模型选项将通过JavaScript动态生成 -->
                </div>
              </div>

              <button
                type="submit"
                id="sendButton"
                class="btn-primary send-btn"
                disabled
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
              <button
                type="button"
                id="stopButton"
                class="stop-btn hidden"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z" />
                </svg>
              </button>
            </div>
          </form>
          <div class="text-xs text-center mt-2" style="color: var(--text-tertiary);">
            按 Enter 发送，Shift + Enter 换行
          </div>
        </div>
      </div>
    </div>
  </div>

  <script th:inline="javascript">
    // 配置 marked 以支持单个换行和更宽松的 Markdown 解析
    marked.setOptions({
      breaks: true, // 单个换行符视为 <br>
      gfm: true, // 启用 GitHub Flavored Markdown
      highlight: function(code, lang) {
        return code;
      }
    });

    // 获取服务器传递的消息（如果有）
    const serverMessage = document.getElementById('serverMessage')?.textContent;
    if (serverMessage) {
      console.log('Server message:', serverMessage);
    }

    // 应用状态
    const state = {
      chats: [],
      currentChat: null,
      messages: [],
      isStreaming: false,
      controller: null,
      // 流式思维链状态
      streamingThinkingChain: {
        isInThinking: false,
        currentThinkingId: null,
        thinkingContent: '',
        thinkingStartIndex: -1
      }
    };

    // DOM元素
    const elements = {
      newChatBtn: document.getElementById('newChatBtn'),
      chatList: document.getElementById('chatList'),
      noChatMessage: document.getElementById('noChatMessage'),
      messageContainer: document.getElementById('messageContainer'),
      emptyState: document.getElementById('emptyState'),
      messagesWrapper: document.getElementById('messagesWrapper'),
      messageForm: document.getElementById('messageForm'),
      messageInput: document.getElementById('messageInput'),
      sendButton: document.getElementById('sendButton'),
      stopButton: document.getElementById('stopButton'),
      logoutBtn: document.getElementById('logoutBtn'),
      // 新的工具选择器元素
      toolSelector: document.getElementById('toolSelector'),
      toolTrigger: document.getElementById('toolTrigger'),
      toolTriggerText: document.getElementById('toolTriggerText'),
      toolDropdown: document.getElementById('toolDropdown'),
      // 模型提供商选择元素
      modelProviderBtn: document.getElementById('modelProviderBtn'),
      modelProviderText: document.getElementById('modelProviderText'),
      modelProviderDropdown: document.getElementById('modelProviderDropdown'),
      modelProviderContainer: document.querySelector('.model-provider-container'),
      // Thinking按钮元素
      thinkingBtn: document.getElementById('thinkingBtn'),
      // 移动端元素
      mobileMenuBtn: document.getElementById('mobileMenuBtn'),
      sidebar: document.getElementById('sidebar'),
      sidebarOverlay: document.getElementById('sidebarOverlay')
    };

    // 工具状态
    const toolState = {
      tools: [],
      selectedTool: null,
      isDropdownOpen: false
    };

    // 模型提供商状态
    const modelProviderState = {
      providers: [],
      selectedProvider: null,
      selectedModel: null,
      isDropdownOpen: false
    };

    // Thinking状态
    const thinkingState = {
      isThinking: false
    };

    // 获取指定名称的 cookie 值
    function getCookie(name) {
      const nameEQ = name + "=";
      const ca = document.cookie.split(';');
      for(let i=0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
      }
      return '你';
    }

    // 创建新对话的函数
    function createNewChat(title = '新对话') {
      return {
        id: Date.now().toString(),
        title,
        messages: [],
        createdAt: new Date().toISOString()
      };
    }

    // 格式化时间
    function formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // 处理思维链内容
    function processThinkingChain(content) {
      if (!content) return '';

      // 使用正则表达式匹配 <think>...</think> 标签
      const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
      let processedContent = content;
      let match;
      let thinkingChainId = 0;
      const replacements = [];

      // 收集所有的 <think> 标签匹配
      while ((match = thinkRegex.exec(content)) !== null) {
        const thinkingContent = match[1].trim();
        const thinkingChainHtml = createThinkingChainHtml(thinkingContent, thinkingChainId++);
        replacements.push({
          original: match[0],
          replacement: thinkingChainHtml,
          index: match.index
        });
      }

      // 从后往前替换，避免索引偏移问题
      replacements.reverse().forEach(replacement => {
        processedContent = processedContent.substring(0, replacement.index) +
                          replacement.replacement +
                          processedContent.substring(replacement.index + replacement.original.length);
      });

      // 处理剩余的markdown内容
      const thinkingChainPlaceholder = '___THINKING_CHAIN_PLACEHOLDER___';
      const thinkingChains = [];
      let placeholderIndex = 0;

      // 提取思维链HTML并用占位符替换
      processedContent = processedContent.replace(/<div class="thinking-chain"[\s\S]*?<\/div>/g, (match) => {
        thinkingChains.push(match);
        return `${thinkingChainPlaceholder}${placeholderIndex++}${thinkingChainPlaceholder}`;
      });

      // 处理普通markdown内容
      let result = marked.parse(processedContent);

      // 恢复思维链HTML
      thinkingChains.forEach((chain, index) => {
        const placeholder = `${thinkingChainPlaceholder}${index}${thinkingChainPlaceholder}`;
        result = result.replace(placeholder, chain);
      });

      return result;
    }

    // 创建思维链HTML
    function createThinkingChainHtml(content, id) {
      const uniqueId = `thinking-chain-${Date.now()}-${id}`;
      return `
        <div class="thinking-chain" data-thinking-id="${uniqueId}">
          <div class="thinking-chain-header" onclick="toggleThinkingChain('${uniqueId}')">
            <div class="thinking-chain-title">
              <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
              <span>思维过程</span>
              <span class="thinking-chain-badge">展开查看</span>
            </div>
          </div>
          <div class="thinking-chain-content" id="${uniqueId}-content">
            <div class="thinking-chain-inner">${escapeHtml(content)}</div>
          </div>
        </div>
      `;
    }

    // HTML转义函数
    function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    // 切换思维链展开/折叠状态
    function toggleThinkingChain(id) {
      const header = document.querySelector(`[data-thinking-id="${id}"] .thinking-chain-header`);
      const content = document.getElementById(`${id}-content`);
      const badge = header.querySelector('.thinking-chain-badge');

      if (!header || !content || !badge) return;

      const isExpanded = header.classList.contains('expanded');

      if (isExpanded) {
        // 折叠
        header.classList.remove('expanded');
        content.classList.remove('expanded');
        badge.textContent = '展开查看';
      } else {
        // 展开
        header.classList.add('expanded');
        content.classList.add('expanded');
        badge.textContent = '收起';
      }
    }

    // 流式渲染思维链
    function renderStreamingMessage(message) {
      if (!message || message.role !== 'assistant') return;

      // 找到最后一条助手消息的DOM元素
      const messageElements = elements.messagesWrapper.querySelectorAll('.flex.flex-col.mb-6');
      let lastAssistantElement = null;

      for (let i = messageElements.length - 1; i >= 0; i--) {
        if (messageElements[i].classList.contains('items-start')) {
          lastAssistantElement = messageElements[i];
          break;
        }
      }

      if (!lastAssistantElement) return;

      const bubbleElement = lastAssistantElement.querySelector('.message-bubble');
      if (!bubbleElement) return;

      // 处理流式内容
      processStreamingContent(bubbleElement, message.content);
    }

    // 处理流式内容，支持实时思维链渲染
    function processStreamingContent(bubbleElement, content) {
      // 检测思维链标签
      const thinkStartRegex = /<think>/g;
      const thinkEndRegex = /<\/think>/g;

      let currentPos = 0;
      let result = '';

      // 重置流式思维链状态如果内容重新开始
      if (content.length < state.streamingThinkingChain.thinkingContent.length) {
        resetStreamingThinkingState();
      }

      while (currentPos < content.length) {
        if (!state.streamingThinkingChain.isInThinking) {
          // 查找思维链开始标签
          thinkStartRegex.lastIndex = currentPos;
          const thinkStartMatch = thinkStartRegex.exec(content);
          if (thinkStartMatch && thinkStartMatch.index >= currentPos) {
            const startIndex = thinkStartMatch.index;
            // 添加思维链之前的普通内容
            if (startIndex > currentPos) {
              result += content.substring(currentPos, startIndex);
            }

            // 开始思维链
            state.streamingThinkingChain.isInThinking = true;
            state.streamingThinkingChain.thinkingStartIndex = startIndex + thinkStartMatch[0].length;
            state.streamingThinkingChain.currentThinkingId = `thinking-chain-streaming-${Date.now()}`;
            state.streamingThinkingChain.thinkingContent = '';

            // 创建思维链容器
            result += createStreamingThinkingChainHtml(state.streamingThinkingChain.currentThinkingId);

            currentPos = state.streamingThinkingChain.thinkingStartIndex;
          } else {
            // 没有找到思维链开始，添加剩余内容
            result += content.substring(currentPos);
            break;
          }
        } else {
          // 在思维链中，查找结束标签
          thinkEndRegex.lastIndex = currentPos;
          const thinkEndMatch = thinkEndRegex.exec(content);
          if (thinkEndMatch && thinkEndMatch.index >= currentPos) {
            const endIndex = thinkEndMatch.index;
            // 添加思维链内容
            const thinkingContent = content.substring(state.streamingThinkingChain.thinkingStartIndex, endIndex);
            state.streamingThinkingChain.thinkingContent = thinkingContent;

            // 结束思维链
            state.streamingThinkingChain.isInThinking = false;
            currentPos = endIndex + thinkEndMatch[0].length;
          } else {
            // 还在思维链中，更新思维链内容
            const thinkingContent = content.substring(state.streamingThinkingChain.thinkingStartIndex);
            state.streamingThinkingChain.thinkingContent = thinkingContent;
            break;
          }
        }
      }

      // 如果还在思维链中，添加剩余的普通内容
      if (!state.streamingThinkingChain.isInThinking && currentPos < content.length) {
        result += content.substring(currentPos);
      }

      // 更新DOM
      updateStreamingDOM(bubbleElement, result);
    }

    // 创建流式思维链HTML
    function createStreamingThinkingChainHtml(id) {
      return `
        <div class="thinking-chain thinking" data-thinking-id="${id}">
          <div class="thinking-chain-header" onclick="toggleThinkingChain('${id}')">
            <div class="thinking-chain-title">
              <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
              <span>思维过程</span>
              <span class="thinking-chain-badge">思考中...</span>
            </div>
          </div>
          <div class="thinking-chain-content expanded" id="${id}-content">
            <div class="thinking-chain-inner" id="${id}-inner"></div>
          </div>
        </div>
      `;
    }

    // 更新流式DOM
    function updateStreamingDOM(bubbleElement, processedContent) {
      // 使用占位符方法处理思维链和普通内容
      const thinkingChainPlaceholder = '___THINKING_CHAIN_PLACEHOLDER___';
      const thinkingChains = [];
      let placeholderIndex = 0;

      // 提取思维链HTML并用占位符替换
      let contentWithPlaceholders = processedContent.replace(/<div class="thinking-chain[^>]*>[\s\S]*?<\/div>/g, (match) => {
        thinkingChains.push(match);
        return `${thinkingChainPlaceholder}${placeholderIndex++}${thinkingChainPlaceholder}`;
      });

      // 处理普通markdown内容
      let htmlContent = '';
      if (contentWithPlaceholders.trim()) {
        try {
          htmlContent = marked.parse(contentWithPlaceholders);
        } catch (error) {
          console.warn('Markdown parsing error:', error);
          htmlContent = contentWithPlaceholders.replace(/\n/g, '<br>');
        }
      }

      // 恢复思维链HTML
      thinkingChains.forEach((chain, index) => {
        const placeholder = `${thinkingChainPlaceholder}${index}${thinkingChainPlaceholder}`;
        htmlContent = htmlContent.replace(placeholder, chain);
      });

      // 更新内容
      bubbleElement.innerHTML = `<div class="markdown-content">${htmlContent}</div>`;

      // 更新当前思维链内容
      if (state.streamingThinkingChain.isInThinking && state.streamingThinkingChain.currentThinkingId) {
        const thinkingInner = document.getElementById(`${state.streamingThinkingChain.currentThinkingId}-inner`);
        if (thinkingInner) {
          thinkingInner.textContent = state.streamingThinkingChain.thinkingContent;
          thinkingInner.classList.add('typing');
        }
      }

      // 如果思维链结束，更新状态
      if (!state.streamingThinkingChain.isInThinking && state.streamingThinkingChain.currentThinkingId) {
        const thinkingChain = document.querySelector(`[data-thinking-id="${state.streamingThinkingChain.currentThinkingId}"]`);
        const badge = thinkingChain?.querySelector('.thinking-chain-badge');
        const thinkingInner = thinkingChain?.querySelector('.thinking-chain-inner');
        if (thinkingChain && badge) {
          thinkingChain.classList.remove('thinking');
          badge.textContent = '展开查看';
          if (thinkingInner) {
            thinkingInner.classList.remove('typing');
          }
          // 默认折叠完成的思维链
          const header = thinkingChain.querySelector('.thinking-chain-header');
          const content = thinkingChain.querySelector('.thinking-chain-content');
          if (header && content) {
            header.classList.remove('expanded');
            content.classList.remove('expanded');
          }
        }
      }
    }

    // 重置流式思维链状态
    function resetStreamingThinkingState() {
      state.streamingThinkingChain = {
        isInThinking: false,
        currentThinkingId: null,
        thinkingContent: '',
        thinkingStartIndex: -1
      };
    }

    // 初始化应用
    function initApp() {
      // 加载保存的聊天记录
      const savedChats = JSON.parse(localStorage.getItem('chats') || '[]');
      state.chats = savedChats;

      // 如果有聊天记录，选择第一个
      if (savedChats.length > 0) {
        state.currentChat = savedChats[0].id;
        state.messages = savedChats[0].messages;
      }

      // 加载模型提供商状态
      const savedModelProvider = localStorage.getItem('selectedModelProvider');
      const savedModel = localStorage.getItem('selectedModel');
      if (savedModelProvider && savedModel) {
        modelProviderState.selectedProvider = savedModelProvider;
        modelProviderState.selectedModel = savedModel;
      }
      // 如果没有保存的状态，将在loadModelProviders中设置默认选择

      // 加载thinking状态
      const savedThinking = localStorage.getItem('isThinking');
      if (savedThinking === 'true') {
        thinkingState.isThinking = true;
      }


      // 渲染UI
      renderChatList();
      renderMessages();

      // 设置事件监听器
      setupEventListeners();

      // 确保滚动到底部
      setTimeout(() => {
        elements.messageContainer.scrollTop = elements.messageContainer.scrollHeight;
      }, 100);

      // 加载工具列表
      loadTools();

      // 加载模型提供商列表
      loadModelProviders();

      // 初始化UI状态
      updateThinkingUI();
      updateModelProviderState();
    }

    // 加载工具列表
    async function loadTools() {
      try {
        const response = await fetch('/api/tools');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const tools = await response.json();
        toolState.tools = tools;
        renderToolDropdown();
      } catch (error) {
        console.error('Error loading tools:', error);
        // 可以添加错误提示给用户
      }
    }

    // 渲染工具下拉框
    function renderToolDropdown() {
      elements.toolDropdown.innerHTML = '';

      // 添加"无工具"选项
      const noneOption = document.createElement('div');
      noneOption.className = `tool-option ${!toolState.selectedTool ? 'selected' : ''}`;
      noneOption.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="tool-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
        <div>
          <div class="tool-name">无工具</div>
          <div class="tool-description">不使用任何工具</div>
        </div>
      `;
      noneOption.addEventListener('click', () => selectTool(null));
      elements.toolDropdown.appendChild(noneOption);

      // 添加工具选项
      toolState.tools.forEach(tool => {
        const option = document.createElement('div');
        option.className = `tool-option ${toolState.selectedTool?.name === tool.name ? 'selected' : ''}`;
        option.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" class="tool-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 9.172V5L8 4z" />
          </svg>
          <div>
            <div class="tool-name">${tool.name}</div>
            <div class="tool-description">${tool.description}</div>
          </div>
        `;
        option.addEventListener('click', () => selectTool(tool));
        elements.toolDropdown.appendChild(option);
      });
    }

    // 选择工具
    function selectTool(tool) {
      toolState.selectedTool = tool;
      elements.toolTriggerText.textContent = tool ? tool.name : '选择工具';
      elements.toolTrigger.classList.toggle('active', !!tool);
      renderToolDropdown();
      toggleToolDropdown(false);
    }

    // 切换工具下拉框显示状态
    function toggleToolDropdown(show = null) {
      if (show === null) {
        toolState.isDropdownOpen = !toolState.isDropdownOpen;
      } else {
        toolState.isDropdownOpen = show;
      }

      elements.toolDropdown.classList.toggle('show', toolState.isDropdownOpen);
      elements.toolTrigger.classList.toggle('active', toolState.isDropdownOpen);
    }

    // 加载模型提供商列表
    async function loadModelProviders() {
      try {
        const response = await fetch('/api/providers');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const providers = await response.json();
        modelProviderState.providers = providers;

        // 如果用户没有保存的选择，默认选中第一个模型
        if (!modelProviderState.selectedProvider && !modelProviderState.selectedModel && providers.length > 0) {
          const firstProvider = providers[0];
          if (firstProvider.models && firstProvider.models.length > 0) {
            const firstModel = firstProvider.models[0];
            modelProviderState.selectedProvider = firstProvider.provider;
            modelProviderState.selectedModel = firstModel.model;
            saveModelProviderState();
          }
        }

        renderModelProviderDropdown();
        updateModelProviderUI();
      } catch (error) {
        console.error('Error loading model providers:', error);
      }
    }

    // 渲染模型提供商下拉框
    function renderModelProviderDropdown() {
      elements.modelProviderDropdown.innerHTML = '';

      modelProviderState.providers.forEach(provider => {
        // 创建提供商组
        const providerGroup = document.createElement('div');
        providerGroup.className = 'provider-group';

        // 提供商标题
        const providerHeader = document.createElement('div');
        providerHeader.className = 'provider-header';
        providerHeader.textContent = provider.name || provider.provider;
        providerGroup.appendChild(providerHeader);

        // 模型选项
        provider.models.forEach(model => {
          const modelOption = document.createElement('div');
          modelOption.className = `model-option ${
            modelProviderState.selectedProvider === provider.provider &&
            modelProviderState.selectedModel === model.model ? 'selected' : ''
          }`;

          modelOption.innerHTML = `
            <div class="model-name">${model.modelName || model.model}</div>
            <div class="model-description">${model.description || '暂无描述'}</div>
          `;

          modelOption.addEventListener('click', () => selectModel(provider.provider, model));
          providerGroup.appendChild(modelOption);
        });

        elements.modelProviderDropdown.appendChild(providerGroup);
      });
    }

    // 选择模型
    function selectModel(provider, model) {
      modelProviderState.selectedProvider = provider;
      modelProviderState.selectedModel = model.model;
      updateModelProviderUI();
      saveModelProviderState();
      toggleModelProviderDropdown(false);
    }

    // 切换模型提供商下拉框
    function toggleModelProviderDropdown(show = null) {
      if (show === null) {
        modelProviderState.isDropdownOpen = !modelProviderState.isDropdownOpen;
      } else {
        modelProviderState.isDropdownOpen = show;
      }

      elements.modelProviderDropdown.classList.toggle('show', modelProviderState.isDropdownOpen);
      elements.modelProviderBtn.classList.toggle('active', modelProviderState.isDropdownOpen);
    }

    // 更新模型提供商UI
    function updateModelProviderUI() {
      let displayText = '选择模型';
      let tooltipText = '选择大模型';

      if (modelProviderState.selectedProvider && modelProviderState.selectedModel) {
        const provider = modelProviderState.providers.find(p => p.provider === modelProviderState.selectedProvider);
        if (provider) {
          const model = provider.models.find(m => m.model === modelProviderState.selectedModel);
          if (model) {
            displayText = model.modelName || model.model;
            tooltipText = `当前模型：${displayText}`;
          }
        }
      }

      elements.modelProviderText.textContent = displayText;
      elements.modelProviderBtn.title = tooltipText;
      renderModelProviderDropdown(); // 重新渲染以更新选中状态
    }

    // 保存模型提供商状态
    function saveModelProviderState() {
      if (modelProviderState.selectedProvider) {
        localStorage.setItem('selectedModelProvider', modelProviderState.selectedProvider);
      }
      if (modelProviderState.selectedModel) {
        localStorage.setItem('selectedModel', modelProviderState.selectedModel);
      }
    }

    // Thinking按钮相关函数
    function toggleThinking() {
      thinkingState.isThinking = !thinkingState.isThinking;
      updateThinkingUI();
      updateModelProviderState();

      // 保存thinking状态
      localStorage.setItem('isThinking', thinkingState.isThinking.toString());
    }

    function updateThinkingUI() {
      elements.thinkingBtn.classList.toggle('active', thinkingState.isThinking);
      elements.thinkingBtn.title = thinkingState.isThinking ? '退出深度思考模式' : '深度思考模式';
    }

    function updateModelProviderState() {
      // thinking模式下禁用模型提供商选择
      elements.modelProviderBtn.classList.toggle('disabled', thinkingState.isThinking);
      if (thinkingState.isThinking) {
        // 关闭模型提供商下拉框
        toggleModelProviderDropdown(false);
      }
    }



    // 渲染聊天列表
    function renderChatList() {
      // 清空现有列表
      elements.chatList.innerHTML = '';

      if (state.chats.length === 0) {
        // 显示"没有对话记录"消息
        elements.noChatMessage.classList.remove('hidden');
        return;
      }

      // 隐藏"没有对话记录"消息
      elements.noChatMessage.classList.add('hidden');

      // 添加新的聊天项
      state.chats.forEach(chat => {
        const chatItem = document.createElement('div');
        chatItem.className = `py-3 px-3 cursor-pointer text-sm flex justify-between items-center chat-item ${
          state.currentChat === chat.id ? 'active' : ''
        }`;
        chatItem.dataset.chatId = chat.id;
        chatItem.style.color = state.currentChat === chat.id ? 'var(--accent-primary)' : 'var(--text-secondary)';

        chatItem.innerHTML = `
          <div class="flex items-center truncate flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            <span class="truncate font-medium">${chat.title}</span>
          </div>
          <button
            class="ml-2 p-1 rounded-full delete-btn opacity-0 group-hover:opacity-100 transition-opacity"
            style="color: var(--text-tertiary);"
            title="删除对话"
            data-chat-id="${chat.id}"
            onmouseover="this.style.color='var(--error)'"
            onmouseout="this.style.color='var(--text-tertiary)'"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        `;

        // 添加hover效果
        chatItem.addEventListener('mouseenter', () => {
          if (state.currentChat !== chat.id) {
            chatItem.style.color = 'var(--text-primary)';
          }
          chatItem.querySelector('.delete-btn').style.opacity = '1';
        });

        chatItem.addEventListener('mouseleave', () => {
          if (state.currentChat !== chat.id) {
            chatItem.style.color = 'var(--text-secondary)';
          }
          chatItem.querySelector('.delete-btn').style.opacity = '0';
        });

        elements.chatList.appendChild(chatItem);
      });
    }

    // 渲染消息
    function renderMessages() {
      // 清空现有消息
      elements.messagesWrapper.innerHTML = '';
      
      if (!state.messages || state.messages.length === 0) {
        // 显示空状态
        elements.emptyState.classList.remove('hidden');
        elements.messagesWrapper.classList.add('hidden');
        return;
      }
      
      // 隐藏空状态，显示消息容器
      elements.emptyState.classList.add('hidden');
      elements.messagesWrapper.classList.remove('hidden');
      
      // 添加新的消息
      state.messages.forEach(message => {
        const messageElement = document.createElement('div');
        messageElement.className = `flex flex-col mb-6 ${message.role === 'user' ? 'items-end' : 'items-start'}`;
        
        const headerElement = document.createElement('div');
        headerElement.className = `flex items-center mb-2 ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`;
        
        const avatarElement = document.createElement('div');
        avatarElement.className = `avatar ${
          message.role === 'user' ? 'user-avatar ml-2' : 'assistant-avatar mr-2'
        }`;
        avatarElement.textContent = message.role === 'user' ? '👤' : '🤖';
        
        const nameElement = document.createElement('div');
        nameElement.className = 'text-sm font-medium';
        if (message.role === 'user') {
          const username = getCookie('username'); // 假设用户名为 'username' 的 cookie
          nameElement.textContent = username ? username : '你';
        } else {
          nameElement.textContent = 'Yo';
        }
        
        const timeElement = document.createElement('div');
        timeElement.className = `text-xs text-gray-500 ${message.role === 'user' ? 'mr-2' : 'ml-2'}`;
        timeElement.textContent = formatTime(message.timestamp);
        
        headerElement.appendChild(avatarElement);
        headerElement.appendChild(nameElement);
        headerElement.appendChild(timeElement);
        
        const bubbleElement = document.createElement('div');
        bubbleElement.className = `message-bubble ${
          message.role === 'user' ? 'user-message mr-10' : 'assistant-message ml-10'
        }`;
        
        if (message.role === 'assistant') {
          // 检查是否需要显示loading状态
          if (state.isStreaming && (message.content === '' || message.content.trim() === '')) {
            // 流式开始时，重置思维链状态
            resetStreamingThinkingState();

            const typingIndicator = document.createElement('div');
            typingIndicator.className = 'typing-indicator';
            typingIndicator.innerHTML = '<span></span><span></span><span></span>';
            bubbleElement.appendChild(typingIndicator);
          } else {
            const processedContent = processThinkingChain(message.content);
            const contentElement = document.createElement('div');
            contentElement.className = 'markdown-content';
            contentElement.innerHTML = processedContent;
            bubbleElement.appendChild(contentElement);
          }
        } else {
          const contentElement = document.createElement('div');
          contentElement.className = 'user-content';
          contentElement.innerHTML = message.content.replace(/\n/g, '<br>');
          
          bubbleElement.appendChild(contentElement);
        }
        
        messageElement.appendChild(headerElement);
        messageElement.appendChild(bubbleElement);
        
        elements.messagesWrapper.appendChild(messageElement);
      });
      
      // 滚动到底部
      setTimeout(() => {
        elements.messageContainer.scrollTop = elements.messageContainer.scrollHeight;
      }, 0);
    }

    // 设置事件监听器
    function setupEventListeners() {
      elements.newChatBtn.addEventListener('click', handleNewChat);
      elements.chatList.addEventListener('click', handleChatListClick);
      elements.messageForm.addEventListener('submit', handleSendMessage);
      elements.messageInput.addEventListener('keydown', handleInputKeyDown);
      elements.messageInput.addEventListener('input', handleInputChange);
      elements.stopButton.addEventListener('click', stopStreaming);
      elements.logoutBtn.addEventListener('click', handleLogout);

      // 工具选择器事件
      elements.toolTrigger.addEventListener('click', () => toggleToolDropdown());

      // Thinking按钮事件
      elements.thinkingBtn.addEventListener('click', toggleThinking);

      // 模型提供商选择事件
      elements.modelProviderBtn.addEventListener('click', (e) => {
        if (!thinkingState.isThinking) {
          toggleModelProviderDropdown();
        }
      });

      // 移动端菜单事件
      elements.mobileMenuBtn.addEventListener('click', toggleMobileSidebar);
      elements.sidebarOverlay.addEventListener('click', () => toggleMobileSidebar(false));

      // 点击外部关闭工具下拉框
      document.addEventListener('click', (event) => {
        if (!elements.toolSelector.contains(event.target)) {
          toggleToolDropdown(false);
        }
        // 点击外部关闭模型提供商下拉框
        if (!elements.modelProviderContainer.contains(event.target)) {
          toggleModelProviderDropdown(false);
        }
      });

      // 响应式处理
      window.addEventListener('resize', handleResize);
    }

    // 移动端侧边栏切换
    function toggleMobileSidebar(show = null) {
      const isOpen = show !== null ? show : !elements.sidebar.classList.contains('open');

      elements.sidebar.classList.toggle('open', isOpen);
      elements.sidebarOverlay.classList.toggle('show', isOpen);

      // 防止背景滚动
      document.body.style.overflow = isOpen ? 'hidden' : '';
    }

    // 响应式处理
    function handleResize() {
      if (window.innerWidth > 768) {
        // 桌面端：关闭移动端侧边栏
        toggleMobileSidebar(false);
      }
    }

    // 处理新建对话
    function handleNewChat() {
      const newChat = createNewChat();
      state.chats.unshift(newChat);
      state.currentChat = newChat.id;
      state.messages = [];
      
      // 保存到本地存储
      saveChats();
      
      // 更新UI
      renderChatList();
      renderMessages();
      
      // 聚焦输入框
      elements.messageInput.focus();
    }

    // 处理聊天列表点击
    function handleChatListClick(event) {
      // 如果点击的是删除按钮
      if (event.target.closest('.delete-btn')) {
        event.stopPropagation(); // 阻止事件冒泡
        const chatId = event.target.closest('.delete-btn').dataset.chatId;
        handleDeleteChat(chatId);
        return;
      }
      
      // 如果点击的是聊天项
      const chatItem = event.target.closest('.chat-item');
      if (chatItem) {
        const chatId = chatItem.dataset.chatId;
        selectChat(chatId);
      }
    }

    // 选择对话
    function selectChat(chatId) {
      if (state.currentChat === chatId) return;
      
      const chat = state.chats.find(c => c.id === chatId);
      if (chat) {
        state.currentChat = chatId;
        state.messages = chat.messages;
        
        // 更新UI
        renderChatList();
        renderMessages();
      }
    }

    // 处理删除对话
    function handleDeleteChat(chatId) {
      // 过滤掉要删除的对话
      state.chats = state.chats.filter(chat => chat.id !== chatId);
      
      // 如果删除的是当前选中的对话
      if (state.currentChat === chatId) {
        if (state.chats.length > 0) {
          state.currentChat = state.chats[0].id;
          state.messages = state.chats[0].messages;
        } else {
          // 如果没有对话了，重置状态
          state.currentChat = null;
          state.messages = [];
        }
      }
      
      // 保存到本地存储
      saveChats();
      
      // 更新UI
      renderChatList();
      renderMessages();
      
      // 如果没有对话了，显示空状态
      if (state.chats.length === 0) {
        elements.emptyState.classList.remove('hidden');
        elements.messagesWrapper.classList.add('hidden');
      }
    }

    // 处理输入框键盘事件
    function handleInputKeyDown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (elements.messageInput.value.trim() && !state.isStreaming) {
          // 直接调用发送消息函数，而不是触发表单提交事件
          handleSendMessage(event);
        }
      }
    }

    // 处理输入框变化
    function handleInputChange() {
      const inputValue = elements.messageInput.value.trim();
      elements.sendButton.disabled = !inputValue || state.isStreaming;
      
      // 自动调整高度
      elements.messageInput.style.height = 'auto';
      elements.messageInput.style.height = Math.min(elements.messageInput.scrollHeight, 200) + 'px';
    }



    // 处理发送消息
    async function handleSendMessage(event) {
      event.preventDefault();
      
      const messageText = elements.messageInput.value.trim();
      if (!messageText || state.isStreaming) return;
      
      let chatId = state.currentChat;
      let isNewChat = false;
      
      // 如果没有当前对话，创建一个新的
      if (!chatId || state.chats.length === 0) {
        const newChat = createNewChat(messageText.length > 15 ? messageText.substring(0, 15) + '...' : messageText);
        state.chats.unshift(newChat);
        state.currentChat = newChat.id;
        chatId = newChat.id;
        state.messages = [];
        isNewChat = true;
      }
      
      // 创建用户消息
      const userMessage = {
        id: Date.now(),
        content: messageText,
        role: 'user',
        timestamp: new Date().toISOString()
      };
      
      // 添加到消息列表
      state.messages.push(userMessage);
      
      // 更新对话
      const currentChat = state.chats.find(c => c.id === chatId);
      if (currentChat) {
        currentChat.messages = state.messages;
        
        // 如果是新对话或第一条消息，设置标题
        if (isNewChat || currentChat.messages.length === 1) {
          currentChat.title = messageText.length > 15 ? messageText.substring(0, 15) + '...' : messageText;
        }
      }
      
      // 清空输入框
      elements.messageInput.value = '';
      elements.messageInput.style.height = 'auto';
      elements.sendButton.disabled = true;
      
      // 保存到本地存储
      saveChats();
      
      // 更新UI
      renderChatList();
      renderMessages();
      
      // 创建AI消息占位
      const aiMessage = {
        id: Date.now() + 1,
        content: '',
        role: 'assistant',
        timestamp: new Date().toISOString()
      };
      
      // 添加到消息列表
      state.messages.push(aiMessage);
      
      // 更新对话
      if (currentChat) {
        currentChat.messages = state.messages;
      }
      
      // 更新UI
      renderMessages();
      
      // 开始流式响应
      await streamResponse(chatId, messageText);
    }

    // 流式响应
    async function streamResponse(chatId, message) {
      state.isStreaming = true;
      elements.sendButton.classList.add('hidden');
      elements.stopButton.classList.remove('hidden');
      
      try {
        const abortController = new AbortController();
        state.controller = abortController;
        
        const response = await fetch('/api/chat/stream', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Expires': '0',
            'X-Source': 'web'
          },
          credentials: 'include',
          body: JSON.stringify({
            chatId: chatId,
            message: message,
            stream: true,
            tool: toolState.selectedTool?.name || '',
            provider: modelProviderState.selectedProvider || '',
            model: modelProviderState.selectedModel || '',
            thinkingEnabled: thinkingState.isThinking
          }),
          signal: abortController.signal
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        
        while (true) {
          const { value, done } = await reader.read();
          if (done) {
            if (buffer.trim()) {
              console.warn('Remaining buffer:', buffer);
            }
            break;
          }
          
          buffer += decoder.decode(value, { stream: true });
          const eventBlocks = buffer.split('\n\n');
          buffer = eventBlocks.pop(); // 保留最后一个可能不完整的事件块

          for (const block of eventBlocks) {
            // 跳过完全空的事件块,除非它是流中唯一的块或buffer不为空
            if (!block.trim() && eventBlocks.length > 1 && buffer.length === 0) { 
                continue;
            }

            const fieldLines = block.split('\n');
            let currentEventTokenParts = [];
            let isDoneSignal = false;

            for (const fieldLine of fieldLines) {
              if (fieldLine.startsWith('data:')) {
                const dataContent = fieldLine.substring(5); // 移除 "data:"
                if (dataContent.trim() === '[DONE]') {
                  isDoneSignal = true;
                  break; // 停止处理这个block的后续行
                }
                currentEventTokenParts.push(dataContent);
              }
              // 可以根据需要忽略其他SSE字段 (event, id, retry) 和注释 (以:开头)
            }

            if (isDoneSignal) {
              if (reader && typeof reader.cancel === 'function') {
                await reader.cancel(); // 取消读取器
              }
              buffer = ''; // 清理 buffer
              return; // 直接退出 streamResponse 函数
            }
            
            if (currentEventTokenParts.length > 0) {
              const token = currentEventTokenParts.join('\n'); // 用换行符重新组合内容

              const lastMessage = state.messages[state.messages.length - 1];
              if (lastMessage && lastMessage.role === 'assistant') {
                lastMessage.content += token; // token 已不含data:前缀，并保留内部换行

                const currentChat = state.chats.find(c => c.id === chatId);
                if (currentChat) {
                  currentChat.messages = state.messages;
                }
                saveChats();

                // 使用新的流式渲染函数
                renderStreamingMessage(lastMessage);

                // 滚动到底部
                setTimeout(() => {
                  elements.messageContainer.scrollTop = elements.messageContainer.scrollHeight;
                }, 0);
              }
            }
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Stream error:', error);
          
          // 更新最后一条消息，添加错误提示
          const lastMessage = state.messages[state.messages.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.content += '\n[错误: 服务器响应失败，请稍后重试！]';
            
            // 更新对话
            const currentChat = state.chats.find(c => c.id === chatId);
            if (currentChat) {
              currentChat.messages = state.messages;
            }
            
            // 保存到本地存储
            saveChats();
            
            // 更新UI
            renderMessages();
          }
        }
      } finally {
        state.isStreaming = false;
        state.controller = null;
        elements.sendButton.classList.remove('hidden');
        elements.stopButton.classList.add('hidden');
        elements.sendButton.disabled = !elements.messageInput.value.trim();

        // 重置流式思维链状态
        resetStreamingThinkingState();
      }
    }

    // 停止流式响应
    function stopStreaming() {
      if (state.controller) {
        state.controller.abort();
        state.controller = null;
        state.isStreaming = false;
        elements.sendButton.classList.remove('hidden');
        elements.stopButton.classList.add('hidden');
        elements.sendButton.disabled = !elements.messageInput.value.trim();
      }
    }

    // 保存聊天记录到本地存储
    function saveChats() {
      localStorage.setItem('chats', JSON.stringify(state.chats));
    }

    // 处理退出登录
    async function handleLogout() {
      try {
        const response = await fetch('/logout', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          // 退出成功后重定向到登录页面
          window.location.href = '/login.html';
        } else {
          console.error('Logout failed:', response.status);
        }
      } catch (error) {
        console.error('Error during logout:', error);
      }
    }

    // 将toggleThinkingChain函数暴露到全局作用域，以便HTML onclick可以调用
    window.toggleThinkingChain = toggleThinkingChain;

    // 初始化应用
    document.addEventListener('DOMContentLoaded', initApp);
  </script>
</body>
</html>
